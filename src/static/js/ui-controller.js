// UI Controller - Handles UI interactions and state
class UIController {
    constructor() {
        this.currentStudyId = null;
        this.selectedFiles = [];
        this.windowSizeInSeconds = 30;
        this.currentStartTime = 0;
    }

    // Initialize UI event listeners
    initializeUI() {
        // File selection button
        document.getElementById('select-files-btn').addEventListener('click', () => {
            document.getElementById('file-upload').click();
        });

        // File selection change
        document.getElementById('file-upload').addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files);
        });

        // Upload button
        document.getElementById('upload-btn').addEventListener('click', () => {
            this.handleFileUpload();
        });

        // Clear study button
        document.getElementById('clear-study-btn').addEventListener('click', () => {
            this.handleClearStudy();
        });

        // Navigation buttons
        document.getElementById('previous-window').addEventListener('click', () => {
            this.navigatePrevious();
        });

        document.getElementById('next-window').addEventListener('click', () => {
            this.navigateNext();
        });

        // Window size change
        document.getElementById('window-size').addEventListener('change', (e) => {
            this.handleWindowSizeChange(e);
        });

        // Event filter checkboxes
        document.getElementById('show-respiratory').addEventListener('change', () => {
            this.handleEventFilterChange();
        });

        document.getElementById('show-leg-movements').addEventListener('change', () => {
            this.handleEventFilterChange();
        });

        document.getElementById('show-arousals').addEventListener('change', () => {
            this.handleEventFilterChange();
        });

        // Add files button
        document.getElementById('add-files-btn').addEventListener('click', () => {
            document.getElementById('add-files-input').click();
        });

        // Add files input change
        document.getElementById('add-files-input').addEventListener('change', (e) => {
            this.handleAddFiles(e.target.files);
        });

        // Sidebar toggle handlers
        document.getElementById('toggle-patient-info').addEventListener('click', function() {
            const patientInfo = document.getElementById('patient-info');
            if (patientInfo.style.display === 'none') {
                patientInfo.style.display = 'block';
                this.textContent = '[-]';
            } else {
                patientInfo.style.display = 'none';
                this.textContent = '[+]';
            }
        });

        document.getElementById('toggle-files').addEventListener('click', function() {
            const studyFiles = document.getElementById('study-files');
            if (studyFiles.style.display === 'none') {
                studyFiles.style.display = 'block';
                this.textContent = '[-]';
            } else {
                studyFiles.style.display = 'none';
                this.textContent = '[+]';
            }
        });

        document.getElementById('toggle-annotations').addEventListener('click', function() {
            const annotationList = document.getElementById('annotation-list');
            if (annotationList.style.display === 'none') {
                annotationList.style.display = 'block';
                this.textContent = '[-]';
            } else {
                annotationList.style.display = 'none';
                this.textContent = '[+]';
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Left arrow key - previous window
            if (e.key === 'ArrowLeft') {
                this.navigatePrevious();
            }
            // Right arrow key - next window
            else if (e.key === 'ArrowRight') {
                this.navigateNext();
            }
            // F5 key - refresh current view without requiring new file upload
            else if (e.key === 'F5') {
                e.preventDefault(); // Prevent browser refresh
                this.showTempMessage('Refreshing view...');
                if (this.onRefreshView) {
                    this.onRefreshView();
                }
            }
        });

        // Window unload handler
        window.addEventListener('pagehide', async (event) => {
            // If persisted is true, it's a page cache and not a true unload (like refresh)
            // We only want to clean up if it's a true unload (window close)
            if (!event.persisted && this.currentStudyId) {
                // Check if we should clean up or keep for refresh persistence
                const keepForRefresh = localStorage.getItem('currentStudyId') === this.currentStudyId;

                if (!keepForRefresh && this.onCleanupStudy) {
                    this.onCleanupStudy(this.currentStudyId);
                }
            }
        });
    }

    // Handle file selection
    handleFileSelection(files) {
        this.selectedFiles = Array.from(files);

        // Display selected files
        const fileListEl = document.getElementById('file-list');
        const selectedFilesEl = document.getElementById('selected-files');
        selectedFilesEl.innerHTML = '';

        if (this.selectedFiles.length > 0) {
            fileListEl.style.display = 'block';
            document.getElementById('upload-btn').style.display = 'inline-block';

            this.selectedFiles.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.textContent = file.name;
                selectedFilesEl.appendChild(fileItem);
            });
        } else {
            fileListEl.style.display = 'none';
            document.getElementById('upload-btn').style.display = 'none';
        }
    }

    // Handle file upload
    async handleFileUpload() {
        if (this.selectedFiles.length === 0) {
            this.showTempMessage('No files selected');
            return;
        }

        // Show progress bar
        const progressContainer = document.getElementById('progress-container');
        const progressBar = document.getElementById('progress-bar');
        progressContainer.style.display = 'block';
        progressBar.style.width = '0%';

        this.showTempMessage('Uploading files...');

        try {
            if (this.onUploadFiles) {
                const result = await this.onUploadFiles(this.selectedFiles, (percentCompleted) => {
                    progressBar.style.width = percentCompleted + '%';
                });

                if (result.success) {
                    progressBar.style.width = '100%';
                    this.showTempMessage(`${result.file_count} files uploaded successfully`);

                    // Clean up UI
                    setTimeout(() => {
                        progressContainer.style.display = 'none';
                        document.getElementById('file-list').style.display = 'none';
                        document.getElementById('upload-btn').style.display = 'none';
                    }, 2000);

                    // Show viewer controls
                    document.getElementById('viewer-controls').style.display = 'block';
                    document.getElementById('event-controls').style.display = 'block';
                    document.getElementById('viewer-container').style.display = 'flex';
                    document.getElementById('clear-study-btn').style.display = 'inline-block';

                    // Store study ID
                    this.currentStudyId = result.study_id;
                    localStorage.setItem('currentStudyId', this.currentStudyId);

                    // Load the study data
                    if (this.onLoadStudy) {
                        await this.onLoadStudy(result.study_id);
                    }
                } else {
                    this.showTempMessage('Error: ' + (result.error || 'Unknown error'));
                }
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showTempMessage('Upload failed: ' + error.message);
            progressContainer.style.display = 'none';
        }
    }

    // Handle clear study
    async handleClearStudy() {
        if (confirm('Are you sure you want to clear the current study? This will remove it from the server.')) {
            if (this.currentStudyId) {
                try {
                    if (this.onCleanupStudy) {
                        await this.onCleanupStudy(this.currentStudyId);
                    }

                    // Clear localStorage
                    localStorage.removeItem('currentStudyId');

                    // Reset UI
                    document.getElementById('viewer-controls').style.display = 'none';
                    document.getElementById('event-controls').style.display = 'none';
                    document.getElementById('viewer-container').style.display = 'none';
                    document.getElementById('clear-study-btn').style.display = 'none';

                    // Reset variables
                    this.currentStudyId = null;
                    this.selectedFiles = [];
                    this.currentStartTime = 0;

                    this.showTempMessage('Study cleared successfully');
                } catch (error) {
                    console.error('Error clearing study:', error);
                    this.showTempMessage('Error clearing study: ' + error.message);
                }
            }
        }
    }

    // Navigate to previous window
    navigatePrevious() {
        this.currentStartTime -= this.windowSizeInSeconds;
        if (this.currentStartTime < 0) this.currentStartTime = 0;
        
        if (this.onNavigate) {
            this.onNavigate(this.currentStartTime, this.windowSizeInSeconds);
        }
    }

    // Navigate to next window
    navigateNext() {
        this.currentStartTime += this.windowSizeInSeconds;
        
        if (this.onNavigate) {
            this.onNavigate(this.currentStartTime, this.windowSizeInSeconds);
        }
    }

    // Navigate to specific time
    navigateToTime(timeInSeconds) {
        this.currentStartTime = timeInSeconds - (this.windowSizeInSeconds / 2);
        if (this.currentStartTime < 0) this.currentStartTime = 0;
        
        if (this.onNavigate) {
            this.onNavigate(this.currentStartTime, this.windowSizeInSeconds);
        }
        
        this.showTempMessage(`Navigated to ${Math.floor(timeInSeconds / 60)}:${Math.floor(timeInSeconds % 60).toString().padStart(2, '0')}`);
    }

    // Handle window size change
    handleWindowSizeChange(e) {
        const newSize = parseInt(e.target.value, 10);
        if (isNaN(newSize) || newSize < 5) {
            this.windowSizeInSeconds = 5;
            e.target.value = 5;
        } else if (newSize > 120) {
            this.windowSizeInSeconds = 120;
            e.target.value = 120;
        } else {
            this.windowSizeInSeconds = newSize;
        }
        
        if (this.onNavigate) {
            this.onNavigate(this.currentStartTime, this.windowSizeInSeconds);
        }
    }

    // Handle event filter change
    handleEventFilterChange() {
        if (this.onEventFilterChange) {
            this.onEventFilterChange();
        }
    }

    // Handle adding files to study
    async handleAddFiles(files) {
        if (files.length === 0) return;

        try {
            this.showTempMessage('Adding files to study...');

            if (this.onAddFiles) {
                const result = await this.onAddFiles(this.currentStudyId, files);

                if (result.success) {
                    this.showTempMessage(`Added ${result.added_files} files to study`);
                    
                    // Reload study data
                    if (this.onLoadStudy) {
                        await this.onLoadStudy(this.currentStudyId);
                    }
                } else {
                    this.showTempMessage('Error: ' + (result.error || 'Unknown error'));
                }
            }
        } catch (error) {
            console.error('Error adding files:', error);
            this.showTempMessage('Error adding files: ' + error.message);
        }

        // Clear the file input
        document.getElementById('add-files-input').value = '';
    }

    // Update patient information
    updatePatientInfo(patientInfo) {
        if (!patientInfo) return;

        // Update patient info fields
        if (patientInfo.name) {
            document.getElementById('patient-name').textContent = patientInfo.name;
        }

        if (patientInfo.id) {
            document.getElementById('patient-id').textContent = patientInfo.id;
        }

        if (patientInfo.birthdate) {
            // Format birthdate if needed
            let birthdate = patientInfo.birthdate;
            if (birthdate.length === 8) {
                // Format YYYYMMDD to YYYY-MM-DD
                birthdate = `${birthdate.substring(0, 4)}-${birthdate.substring(4, 6)}-${birthdate.substring(6, 8)}`;
            }
            document.getElementById('patient-birthdate').textContent = birthdate;
        }

        if (patientInfo.sex) {
            document.getElementById('patient-sex').textContent = patientInfo.sex;
        }
    }

    // Update study file list
    updateStudyFileList(files, studyId) {
        const fileList = document.getElementById('study-file-list');
        fileList.innerHTML = '';

        if (files.length === 0) {
            fileList.innerHTML = '<div class="file-item">No files found</div>';
            return;
        }

        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-item-name" title="${file.filename}">${file.filename}</div>
                <div class="file-item-actions">
                    <button class="delete-file-btn" data-filename="${file.filename}">Delete</button>
                </div>
            `;
            fileList.appendChild(fileItem);
        });

        // Add event listeners for delete buttons
        document.querySelectorAll('.delete-file-btn').forEach(button => {
            button.addEventListener('click', async () => {
                const filename = button.getAttribute('data-filename');
                if (confirm(`Are you sure you want to delete ${filename}?`)) {
                    if (this.onDeleteFile) {
                        await this.onDeleteFile(studyId, filename);
                    }
                }
            });
        });
    }

    // Show temporary message
    showTempMessage(message) {
        // Remove any existing message
        const existingMsg = document.querySelector('.temp-message');
        if (existingMsg) {
            existingMsg.remove();
        }

        // Create new message
        const msgDiv = document.createElement('div');
        msgDiv.className = 'temp-message';
        msgDiv.textContent = message;
        document.body.appendChild(msgDiv);

        // Remove after 2 seconds
        setTimeout(() => {
            msgDiv.style.opacity = '0';
            setTimeout(() => msgDiv.remove(), 500);
        }, 2000);
    }
}

export default UIController;
