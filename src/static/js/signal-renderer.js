// Signal Renderer - <PERSON>les rendering signal data to canvas
class SignalRenderer {
    constructor() {
        this.signalData = {};
        this.currentStartTime = 0;
        this.windowSizeInSeconds = 30;
    }

    // Initialize signal containers
    initializeSignalContainers(signalFiles, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        for (const signalFile of signalFiles) {
            // Create canvas for each signal type
            const canvasDiv = document.createElement('div');
            canvasDiv.className = 'signal-canvas';
            canvasDiv.style.height = '160px'; // Default height

            // Format the signal type name for better display
            const signalTypeName = signalFile.type.replace(/([A-Z])/g, ' $1').trim();
            const formattedType = signalTypeName.charAt(0).toUpperCase() + signalTypeName.slice(1);

            canvasDiv.innerHTML = `
                <div class="signal-header">${formattedType}</div>
                <div class="signal-plot-area">
                    <div class="channel-labels" id="labels-${signalFile.type}"></div>
                    <canvas id="signal-${signalFile.type}"></canvas>
                    <div class="signal-x-axis" id="axis-${signalFile.type}"></div>
                </div>
            `;
            container.appendChild(canvasDiv);
        }
    }

    // Draw signal data on canvas
    drawSignal(signalType, timeStart, timeEnd, annotations) {
        if (!this.signalData[signalType]) return;

        const canvas = document.getElementById(`signal-${signalType}`);
        const ctx = canvas.getContext('2d');
        const labelsContainer = document.getElementById(`labels-${signalType}`);

        // Set canvas size
        canvas.width = canvas.parentElement.clientWidth - 110;
        canvas.height = canvas.parentElement.clientHeight - 30;
        canvas.style.marginLeft = '110px';

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        labelsContainer.innerHTML = '';

        const channels = this.signalData[signalType];
        if (channels.length === 0) return;

        // For each channel in this signal type
        channels.forEach((channel, idx) => {
            const visibleData = channel.data;
            const maxValue = Math.max(...visibleData.map(d => Math.abs(d))) || 1;

            // Use consistent height for each channel
            const fixedChannelHeight = 100;
            const buffer = 10;
            const adjustedChannelHeight = fixedChannelHeight - buffer;
            const yOffset = idx * fixedChannelHeight + adjustedChannelHeight/2 + buffer;

            // Draw the signal
            ctx.beginPath();
            ctx.moveTo(0, yOffset);

            for (let i = 0; i < visibleData.length; i++) {
                const x = (i / visibleData.length) * canvas.width;
                const amplitude = (visibleData[i] / maxValue) * (adjustedChannelHeight/2 - 15);
                const y = yOffset - amplitude;
                ctx.lineTo(x, y);
            }

            // Use different colors for different channels
            const colors = ['#2196F3', '#4CAF50', '#9C27B0', '#FF5722', '#795548'];
            ctx.strokeStyle = colors[idx % colors.length];
            ctx.lineWidth = 1.5;
            ctx.stroke();

            // Add channel label
            const labelDiv = document.createElement('div');
            labelDiv.className = 'channel-label';
            labelDiv.textContent = channel.name;
            labelDiv.style.position = 'absolute';
            labelDiv.style.top = `${idx * fixedChannelHeight + (fixedChannelHeight/2) - 20}px`;
            labelDiv.style.borderLeftColor = colors[idx % colors.length];

            // Add units if available
            const unitsSpan = document.createElement('span');
            unitsSpan.className = 'channel-units';
            unitsSpan.textContent = channel.units || 'μV';
            labelDiv.appendChild(unitsSpan);

            // Add value range
            const rangeSpan = document.createElement('span');
            rangeSpan.className = 'channel-range';
            rangeSpan.textContent = `Range: ±${maxValue.toFixed(1)} ${channel.units || 'μV'}`;
            labelDiv.appendChild(rangeSpan);
            console.log(labelDiv);

            labelsContainer.appendChild(labelDiv);
        });

        // Draw x-axis timestamps
        this.drawTimeAxis(signalType, timeStart, timeEnd);

        // Draw visible annotations on the signal
        this.drawAnnotationsOnSignal(signalType, timeStart, timeEnd, annotations);

        // Add hover functionality to show signal values
        this.addSignalValueHover(signalType, timeStart, timeEnd);
    }

    // Draw time axis for a signal
    drawTimeAxis(signalType, timeStart, timeEnd) {
        const axisDiv = document.getElementById(`axis-${signalType}`);
        axisDiv.innerHTML = '';

        const duration = timeEnd - timeStart;
        // Calculate appropriate number of ticks based on duration
        let numTicks;
        if (duration <= 10) {
            numTicks = duration;
        } else if (duration <= 30) {
            numTicks = Math.floor(duration / 2);
        } else if (duration <= 60) {
            numTicks = Math.floor(duration / 5);
        } else {
            numTicks = Math.floor(duration / 10);
        }

        numTicks = Math.max(5, Math.min(numTicks, 15));

        // Add a title to the axis
        const axisTitle = document.createElement('div');
        axisTitle.style.position = 'absolute';
        axisTitle.style.left = '50%';
        axisTitle.style.transform = 'translateX(-50%)';
        axisTitle.style.bottom = '0';
        axisTitle.style.fontSize = '10px';
        axisTitle.style.fontWeight = 'bold';
        axisTitle.style.color = '#666';
        axisTitle.textContent = 'Time (mm:ss)';
        axisDiv.appendChild(axisTitle);

        // Draw the main axis line
        const axisLine = document.createElement('div');
        axisLine.style.position = 'absolute';
        axisLine.style.left = '0';
        axisLine.style.right = '0';
        axisLine.style.top = '0';
        axisLine.style.height = '2px';
        axisLine.style.backgroundColor = '#666';
        axisDiv.appendChild(axisLine);

        for (let i = 0; i <= numTicks; i++) {
            const position = (i / numTicks);
            const time = timeStart + position * duration;
            const minutes = Math.floor(time / 60);
            const seconds = Math.floor(time % 60);

            // Create tick mark
            const tickMark = document.createElement('div');
            tickMark.className = 'time-tick-mark';
            tickMark.style.left = `${position * 100}%`;
            tickMark.style.top = '0';
            tickMark.style.height = '8px';
            axisDiv.appendChild(tickMark);

            // Create tick label
            const tickLabel = document.createElement('div');
            tickLabel.className = 'time-tick';
            tickLabel.style.left = `${position * 100}%`;
            tickLabel.style.top = '10px';
            tickLabel.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            axisDiv.appendChild(tickLabel);
        }
    }

    // Draw annotations on a signal
    drawAnnotationsOnSignal(signalType, timeStart, timeEnd, annotations) {
        const canvas = document.getElementById(`signal-${signalType}`);
        const ctx = canvas.getContext('2d');

        // Remove any existing tooltips
        document.querySelectorAll('.annotation-tooltip').forEach(el => el.remove());

        // Find annotations in the current time window
        const visibleAnnotations = annotations.filter(a => {
            return (a.start >= timeStart && a.start < timeEnd) ||
                (a.end > timeStart && a.end <= timeEnd) ||
                (a.start <= timeStart && a.end >= timeEnd);
        });

        // Draw each annotation
        visibleAnnotations.forEach(annotation => {
            // Get event type information
            const eventInfo = this.getEventTypeInfo(annotation);

            // Skip if not visible
            if (!eventInfo.visible) return;

            // Calculate position
            const startX = Math.max(0, ((annotation.start - timeStart) / (timeEnd - timeStart)) * canvas.width);
            const endX = Math.min(canvas.width, ((annotation.end - timeStart) / (timeEnd - timeStart)) * canvas.width);

            // Draw annotation background with gradient
            const gradient = ctx.createLinearGradient(startX, 0, endX, 0);
            gradient.addColorStop(0, eventInfo.color);
            gradient.addColorStop(1, eventInfo.color.replace('0.2', '0.1'));
            ctx.fillStyle = gradient;
            ctx.fillRect(startX, 0, endX - startX, canvas.height);

            // Draw annotation border
            ctx.strokeStyle = eventInfo.borderColor;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(startX, 0);
            ctx.lineTo(startX, canvas.height);
            ctx.moveTo(endX, 0);
            ctx.lineTo(endX, canvas.height);
            ctx.stroke();

            // Draw annotation label if it fits
            if (endX - startX > 40) {
                ctx.font = 'bold 10px Arial';
                const textWidth = ctx.measureText(eventInfo.displayName).width;

                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                const bgX = startX + 2;
                const bgY = eventInfo.category === 'sleepStage' ? 5 : 25;
                const bgWidth = textWidth + 6;
                const bgHeight = 14;
                const radius = 3;

                // Draw rounded rectangle
                ctx.beginPath();
                ctx.moveTo(bgX + radius, bgY);
                ctx.lineTo(bgX + bgWidth - radius, bgY);
                ctx.quadraticCurveTo(bgX + bgWidth, bgY, bgX + bgWidth, bgY + radius);
                ctx.lineTo(bgX + bgWidth, bgY + bgHeight - radius);
                ctx.quadraticCurveTo(bgX + bgWidth, bgY + bgHeight, bgX + bgWidth - radius, bgY + bgHeight);
                ctx.lineTo(bgX + radius, bgY + bgHeight);
                ctx.quadraticCurveTo(bgX, bgY + bgHeight, bgX, bgY + bgHeight - radius);
                ctx.lineTo(bgX, bgY + radius);
                ctx.quadraticCurveTo(bgX, bgY, bgX + radius, bgY);
                ctx.closePath();
                ctx.fill();

                // Draw text with slight shadow for better readability
                ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
                ctx.fillText(eventInfo.displayName, startX + 4, bgY + 11);
                ctx.fillStyle = 'black';
                ctx.fillText(eventInfo.displayName, startX + 3, bgY + 10);
            }

            // Add hover functionality for annotations
            const annotationArea = document.createElement('div');
            annotationArea.style.position = 'absolute';
            annotationArea.style.left = `${startX + 110}px`; // Account for label container
            annotationArea.style.top = '0';
            annotationArea.style.width = `${endX - startX}px`;
            annotationArea.style.height = `${canvas.height}px`;
            annotationArea.style.zIndex = '5';
            annotationArea.style.cursor = 'pointer';

            // Add tooltip on hover
            annotationArea.addEventListener('mouseover', (e) => {
                const tooltip = document.createElement('div');
                tooltip.className = 'annotation-tooltip';
                tooltip.textContent = `${eventInfo.displayName} (${Math.floor(annotation.start / 60)}:${Math.floor(annotation.start % 60).toString().padStart(2, '0')} - ${Math.floor(annotation.end / 60)}:${Math.floor(annotation.end % 60).toString().padStart(2, '0')})`;
                tooltip.style.left = `${e.clientX + 10}px`;
                tooltip.style.top = `${e.clientY + 10}px`;
                document.body.appendChild(tooltip);
            });

            annotationArea.addEventListener('mousemove', (e) => {
                const tooltip = document.querySelector('.annotation-tooltip');
                if (tooltip) {
                    tooltip.style.left = `${e.clientX + 10}px`;
                    tooltip.style.top = `${e.clientY + 10}px`;
                }
            });

            annotationArea.addEventListener('mouseout', () => {
                const tooltip = document.querySelector('.annotation-tooltip');
                if (tooltip) tooltip.remove();
            });

            // Navigate to annotation on click
            annotationArea.addEventListener('click', () => {
                if (this.onAnnotationClick) {
                    this.onAnnotationClick(annotation.start);
                }
            });

            canvas.parentElement.appendChild(annotationArea);
        });
    }

    // Add hover functionality to show signal values
    addSignalValueHover(signalType, timeStart, timeEnd) {
        const canvas = document.getElementById(`signal-${signalType}`);
        const plotArea = canvas.parentElement;
        const channels = this.signalData[signalType];

        if (!channels || channels.length === 0) return;

        // Remove any existing hover overlay
        const existingOverlay = plotArea.querySelector('.signal-hover-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create a transparent overlay for mouse tracking
        const overlay = document.createElement('div');
        overlay.className = 'signal-hover-overlay';
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '110px'; // Match the canvas offset
        overlay.style.width = `${canvas.width}px`;
        overlay.style.height = `${canvas.height}px`;
        overlay.style.zIndex = '20';
        overlay.style.cursor = 'crosshair';

        // Add mouse event listeners
        overlay.addEventListener('mousemove', (e) => {
            // Calculate relative position within the canvas
            const rect = overlay.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Calculate time at this x position
            const timeRatio = x / canvas.width;
            const currentTime = timeStart + (timeEnd - timeStart) * timeRatio;

            // Format time as mm:ss
            const minutes = Math.floor(currentTime / 60);
            const seconds = Math.floor(currentTime % 60);
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            // Find the closest data point for each channel
            let tooltipHTML = '';

            channels.forEach((channel, idx) => {
                const dataIndex = Math.floor((channel.data.length - 1) * timeRatio);
                if (dataIndex >= 0 && dataIndex < channel.data.length) {
                    const value = channel.data[dataIndex];
                    const units = channel.units || 'μV';

                    // Add to tooltip content - more compact format
                    tooltipHTML += `<div style="color:${['#2196F3', '#4CAF50', '#9C27B0', '#FF5722', '#795548'][idx % 5]}; margin:2px 0; white-space:nowrap;">
                        <b>${channel.name}:</b> ${value.toFixed(2)} ${units}
                    </div>`;
                }
            });

            // Add time to tooltip
            tooltipHTML += `<div style="margin-top:4px; border-top:1px solid #555; padding-top:3px; text-align:center; font-weight:bold;">
                ${timeString}
            </div>`;

            // Create a compact tooltip that stays near the signal
            let tooltip = document.querySelector('.signal-tooltip');
            if (!tooltip) {
                tooltip = document.createElement('div');
                tooltip.className = 'signal-tooltip';
                document.body.appendChild(tooltip);
            }

            tooltip.innerHTML = tooltipHTML;

            // Position the tooltip near the cursor but fixed to the signal area
            const plotRect = plotArea.getBoundingClientRect();

            // Calculate position to keep tooltip within the viewport
            let tooltipX = e.clientX + 15;
            let tooltipY = e.clientY - 40; // Position above cursor

            // Keep tooltip within the signal area horizontally
            if (tooltipX + 200 > plotRect.right) { // Assuming max tooltip width is 200px
                tooltipX = e.clientX - 215; // Position to the left of cursor
            }

            // Keep tooltip within the viewport vertically
            if (tooltipY < 10) {
                tooltipY = e.clientY + 25; // Position below cursor if too close to top
            }

            tooltip.style.left = `${tooltipX}px`;
            tooltip.style.top = `${tooltipY}px`;

            // Draw vertical line at cursor position
            this.drawCursorLine(signalType, x);
        });

        overlay.addEventListener('mouseout', () => {
            // Remove tooltip and cursor line when mouse leaves
            const tooltip = document.querySelector('.signal-tooltip');
            if (tooltip) tooltip.remove();

            // Remove cursor line
            this.removeCursorLine(signalType);
        });

        plotArea.appendChild(overlay);
    }

    // Draw a vertical line at cursor position
    drawCursorLine(signalType, x) {
        const canvas = document.getElementById(`signal-${signalType}`);
        const plotArea = canvas.parentElement;

        // Remove any existing cursor line
        this.removeCursorLine(signalType);

        // Create cursor line
        const cursorLine = document.createElement('div');
        cursorLine.className = 'signal-cursor-line';
        cursorLine.style.position = 'absolute';
        cursorLine.style.top = '0';
        cursorLine.style.left = `${x + 110}px`; // Add offset to match canvas
        cursorLine.style.width = '2px'; // Slightly thicker for better visibility
        cursorLine.style.height = '100%';
        cursorLine.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
        cursorLine.style.zIndex = '15';
        cursorLine.style.pointerEvents = 'none'; // Ensure it doesn't interfere with mouse events

        // Add a small circle at the intersection with the signal
        const dataPoint = document.createElement('div');
        dataPoint.className = 'signal-data-point';
        dataPoint.style.position = 'absolute';
        dataPoint.style.width = '8px';
        dataPoint.style.height = '8px';
        dataPoint.style.borderRadius = '50%';
        dataPoint.style.backgroundColor = '#2196F3';
        dataPoint.style.border = '1px solid white';
        dataPoint.style.left = `${x + 110 - 4}px`; // Center on the line
        dataPoint.style.top = '50%'; // Will be adjusted by channel data
        dataPoint.style.zIndex = '16';
        dataPoint.style.pointerEvents = 'none';

        plotArea.appendChild(cursorLine);

        // Only add data point if we have channel data
        const channels = this.signalData[signalType];
        if (channels && channels.length > 0) {
            // For simplicity, just show for the first channel
            plotArea.appendChild(dataPoint);
        }
    }

    // Remove cursor line and data point
    removeCursorLine(signalType) {
        const canvas = document.getElementById(`signal-${signalType}`);
        const plotArea = canvas.parentElement;

        const cursorLine = plotArea.querySelector('.signal-cursor-line');
        if (cursorLine) cursorLine.remove();

        const dataPoint = plotArea.querySelector('.signal-data-point');
        if (dataPoint) dataPoint.remove();
    }

    // Get event type information
    getEventTypeInfo(annotation) {
        // Initialize default return values
        let result = {
            category: 'unknown',
            displayName: annotation.type_name || annotation.type,
            color: 'rgba(128, 128, 128, 0.2)', // Default gray
            borderColor: 'rgba(128, 128, 128, 0.8)',
            visible: true
        };

        // Check for sleep stage events by code
        if (annotation.type === '2:23672' ||
            annotation.type === '130834' ||
            annotation.type === '130835' ||
            annotation.type === '130836' ||
            annotation.type === '2:23680') {
            result.category = 'sleepStage';
            // Make sleep stages more transparent (0.1 instead of 0.2)
            result.color = 'rgba(0, 0, 255, 0.1)'; // Light blue with higher transparency
            result.borderColor = 'rgba(0, 0, 255, 0.4)'; // Also make borders less dominant

            // Map sleep stage codes to readable names
            if (annotation.type === '2:23672') {
                result.displayName = 'Wake';
            } else if (annotation.type === '130834') {
                result.displayName = 'N1';
            } else if (annotation.type === '130835') {
                result.displayName = 'N2';
            } else if (annotation.type === '130836') {
                result.displayName = 'N3';
            } else if (annotation.type === '2:23680') {
                result.displayName = 'REM';
                result.color = 'rgba(255, 0, 0, 0.1)'; // Very light red
                result.borderColor = 'rgba(255, 0, 0, 0.4)';
            }

            result.visible = true; // Sleep stages are always visible
        }
        // Check for respiratory/apnea events
        else if (annotation.type === '3:3072') {
            result.category = 'respiratory';
            result.color = 'rgba(255, 0, 0, 0.2)'; // Red
            result.borderColor = 'rgba(255, 0, 0, 0.8)';
            result.visible = document.getElementById('show-respiratory').checked;
        }
        // Check for arousal events
        else if (annotation.type === '2:23800') {
            result.category = 'arousal';
            result.color = 'rgba(255, 165, 0, 0.2)'; // Orange
            result.borderColor = 'rgba(255, 165, 0, 0.8)';
            result.visible = document.getElementById('show-arousals').checked;
        }
        // Check for leg/limb movement events
        else if (annotation.type === '2:24184') {
            result.category = 'limb';
            result.color = 'rgba(0, 128, 0, 0.2)'; // Green
            result.borderColor = 'rgba(0, 128, 0, 0.8)';
            result.visible = document.getElementById('show-leg-movements').checked;
        }

        return result;
    }

    // Set the signal data
    setSignalData(signalData) {
        this.signalData = signalData;
    }
}

export default SignalRenderer;
