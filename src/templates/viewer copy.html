<!DOCTYPE html>
<html>
<head>
    <title>PSG DICOM Viewer</title>
    <!-- <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script> -->
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <h1>PSG DICOM Viewer</h1>

    <div class="upload-section">
        <h3>Upload DICOM Files</h3>
        <p>Select multiple DICOM (.dcm) files for viewing. Include both signal and annotation files.</p>
        <input type="file" id="file-upload" class="upload-input" multiple accept=".dcm">
        <button class="upload-btn" id="select-files-btn">Select Files</button>
        <button class="upload-btn" id="upload-btn" style="display:none;">Upload Files</button>
        <button class="upload-btn" id="clear-study-btn" style="background-color: #f44336; display:none;">Clear Current Study</button>

        <div class="file-list" id="file-list" style="display:none;">
            <h4>Selected Files:</h4>
            <div id="selected-files"></div>
        </div>

        <div class="progress-container" id="progress-container">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
    </div>

    <div class="controls" style="display:none;" id="viewer-controls">
        <label>Window Size: <input type="number" id="window-size" value="30" min="5" max="120"> seconds</label>
        <button id="previous-window">Previous</button>
        <button id="next-window">Next</button>
    </div>

    <div class="event-controls" style="display:none;" id="event-controls">
        <label><input type="checkbox" id="show-respiratory" checked> Show Respiratory Events</label>
        <label><input type="checkbox" id="show-leg-movements" checked> Show Leg Movements</label>
        <label><input type="checkbox" id="show-arousals" checked> Show Arousals</label>
    </div>

    <div class="container" id="viewer-container" style="display:none;">
        <div class="sidebar">
            <!-- Patient Information Section -->
            <div class="sidebar-section" id="patient-info-section">
                <h3>
                    Patient Information
                    <span class="sidebar-section-toggle" id="toggle-patient-info">[-]</span>
                </h3>
                <div class="patient-info" id="patient-info">
                    <div class="patient-info-item">
                        <span class="patient-info-label">Name:</span>
                        <span id="patient-name">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">ID:</span>
                        <span id="patient-id">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">Birth Date:</span>
                        <span id="patient-birthdate">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">Sex:</span>
                        <span id="patient-sex">Unknown</span>
                    </div>
                </div>
            </div>

            <!-- Study Files Section -->
            <div class="sidebar-section" id="files-section">
                <h3>
                    Study Files
                    <span class="sidebar-section-toggle" id="toggle-files">[-]</span>
                </h3>
                <div class="study-files" id="study-files">
                    <div class="file-list" id="study-file-list">
                        <!-- Study files will be listed here -->
                    </div>
                    <button class="add-files-btn" id="add-files-btn">Add Files</button>
                    <input type="file" id="add-files-input" class="upload-input" multiple accept=".dcm">
                </div>
            </div>

            <!-- Annotations Section -->
            <div class="sidebar-section" id="annotations-section">
                <h3>
                    Annotations
                    <span class="sidebar-section-toggle" id="toggle-annotations">[-]</span>
                </h3>
                <div class="annotation-list" id="annotation-list"></div>
            </div>
        </div>

        <div class="main-view">
            <div class="hypnogram" id="hypnogram-container">
                <canvas id="hypnogram-canvas"></canvas>
            </div>

            <div class="signals-container" id="signals-container">
                <!-- Signal canvases will be added here -->
            </div>
        </div>
    </div>

    <script>
        let currentStudy = null;
        let currentSignals = [];
        let annotations = [];
        let windowSizeInSeconds = 30;
        let currentStartTime = 0;
        let signalData = {}; // Will store all the signal data
        let selectedFiles = [];
        let currentStudyId = null;

        // Check if we have a stored study ID in localStorage
        const storedStudyId = localStorage.getItem('currentStudyId');

        // Function to initialize the app with the stored study ID
        async function initializeWithStoredStudy() {
            if (storedStudyId) {
                try {
                    // Check if the study still exists on the server
                    const response = await fetch(`/api/study/${storedStudyId}/files`);

                    if (response.ok) {
                        // Study exists, load it
                        currentStudyId = storedStudyId;

                        // Show viewer controls
                        document.getElementById('viewer-controls').style.display = 'block';
                        document.getElementById('event-controls').style.display = 'block';
                        document.getElementById('viewer-container').style.display = 'flex';

                        // Show the clear study button
                        document.getElementById('clear-study-btn').style.display = 'inline-block';

                        // Load the study data
                        await loadStudyData(storedStudyId);
                        showTempMessage('Previous study loaded successfully');
                    } else {
                        // Study doesn't exist anymore, clear the stored ID
                        localStorage.removeItem('currentStudyId');
                    }
                } catch (error) {
                    console.error('Error loading stored study:', error);
                    localStorage.removeItem('currentStudyId');
                }
            }
        }

        // Call the initialization function when the page loads
        window.addEventListener('DOMContentLoaded', initializeWithStoredStudy);

        // Handle clear study button
        document.getElementById('clear-study-btn').addEventListener('click', async function() {
            if (confirm('Are you sure you want to clear the current study? This will remove it from the server.')) {
                if (currentStudyId) {
                    try {
                        // Clean up the study on the server
                        await fetch(`/api/cleanup/${currentStudyId}`, {
                            method: 'DELETE'
                        });

                        // Clear localStorage
                        localStorage.removeItem('currentStudyId');

                        // Reset UI
                        document.getElementById('viewer-controls').style.display = 'none';
                        document.getElementById('event-controls').style.display = 'none';
                        document.getElementById('viewer-container').style.display = 'none';
                        document.getElementById('clear-study-btn').style.display = 'none';

                        // Reset variables
                        currentStudy = null;
                        currentSignals = [];
                        annotations = [];
                        currentStartTime = 0;
                        signalData = {};
                        selectedFiles = [];
                        currentStudyId = null;

                        showTempMessage('Study cleared successfully');
                    } catch (error) {
                        console.error('Error clearing study:', error);
                        showTempMessage('Error clearing study: ' + error.message);
                    }
                }
            }
        });

        // File upload handling
        document.getElementById('select-files-btn').addEventListener('click', function() {
            document.getElementById('file-upload').click();
        });

        document.getElementById('file-upload').addEventListener('change', function(e) {
            selectedFiles = Array.from(e.target.files);

            // Display selected files
            const fileListEl = document.getElementById('file-list');
            const selectedFilesEl = document.getElementById('selected-files');
            selectedFilesEl.innerHTML = '';

            if (selectedFiles.length > 0) {
                fileListEl.style.display = 'block';
                document.getElementById('upload-btn').style.display = 'inline-block';

                selectedFiles.forEach(file => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.textContent = file.name;
                    selectedFilesEl.appendChild(fileItem);
                });
            } else {
                fileListEl.style.display = 'none';
                document.getElementById('upload-btn').style.display = 'none';
            }
        });

        document.getElementById('upload-btn').addEventListener('click', async function() {
            if (selectedFiles.length === 0) {
                showTempMessage('No files selected');
                return;
            }

            // Show progress bar
            const progressContainer = document.getElementById('progress-container');
            const progressBar = document.getElementById('progress-bar');
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';

            showTempMessage('Uploading files...');

            // Create FormData with files
            const formData = new FormData();
            selectedFiles.forEach(file => {
                formData.append('files[]', file);
            });

            try {
                // Upload files
                const response = await fetch('/api/upload-files', {
                    method: 'POST',
                    body: formData,
                    onUploadProgress: (progressEvent) => {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        progressBar.style.width = percentCompleted + '%';
                    }
                });

                const result = await response.json();

                if (result.success) {
                    progressBar.style.width = '100%';
                    showTempMessage(`${result.file_count} files uploaded successfully`);

                    // Clean up UI
                    setTimeout(() => {
                        progressContainer.style.display = 'none';
                        document.getElementById('file-list').style.display = 'none';
                        document.getElementById('upload-btn').style.display = 'none';
                    }, 2000);

                    // Show viewer controls
                    document.getElementById('viewer-controls').style.display = 'block';
                    document.getElementById('event-controls').style.display = 'block';
                    document.getElementById('viewer-container').style.display = 'flex';
                    document.getElementById('clear-study-btn').style.display = 'inline-block';

                    // Load the study data
                    currentStudyId = result.study_id;
                    // Store the study ID in localStorage for persistence across refreshes
                    localStorage.setItem('currentStudyId', currentStudyId);
                    await loadStudyData(result.study_id);
                } else {
                    showTempMessage('Error: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Upload error:', error);
                showTempMessage('Upload failed: ' + error.message);
                progressContainer.style.display = 'none';
            }
        });

        // Load study data when study ID is available
        async function loadStudyData(studyId) {
            showTempMessage('Loading study data...');

            try {
                const response = await fetch(`/api/study/${studyId}`);
                if (!response.ok) {
                    throw new Error('Failed to load study data');
                }

                const studyData = await response.json();

                currentStudy = studyData;
                annotations = studyData.annotations;

                // Load signal data
                await loadSignals(studyData.signals);

                // Populate annotation list
                populateAnnotations(annotations);

                // Draw hypnogram
                drawHypnogram(annotations);

                // Load study files and patient information
                await loadStudyFiles(studyId);

                // Initialize view
                currentStartTime = 0;
                updateView();

                showTempMessage('Study loaded successfully');
            } catch (error) {
                console.error('Error loading study:', error);
                showTempMessage('Error loading study: ' + error.message);
            }
        }

        // Load study files and patient information
        async function loadStudyFiles(studyId) {
            try {
                const response = await fetch(`/api/study/${studyId}/files`);
                if (!response.ok) {
                    throw new Error('Failed to load study files');
                }

                const data = await response.json();
                const files = data.files;

                // Update file list
                updateStudyFileList(files, studyId);

                // Update patient information if available
                updatePatientInfo(files);
            } catch (error) {
                console.error('Error loading study files:', error);
                showTempMessage('Error loading study files: ' + error.message);
            }
        }

        // Update the study file list
        function updateStudyFileList(files, studyId) {
            const fileList = document.getElementById('study-file-list');
            fileList.innerHTML = '';

            if (files.length === 0) {
                fileList.innerHTML = '<div class="file-item">No files found</div>';
                return;
            }

            files.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-item-name" title="${file.filename}">${file.filename}</div>
                    <div class="file-item-actions">
                        <button class="delete-file-btn" data-filename="${file.filename}">Delete</button>
                    </div>
                `;
                fileList.appendChild(fileItem);
            });

            // Add event listeners for delete buttons
            document.querySelectorAll('.delete-file-btn').forEach(button => {
                button.addEventListener('click', async function() {
                    const filename = this.getAttribute('data-filename');
                    if (confirm(`Are you sure you want to delete ${filename}?`)) {
                        await deleteStudyFile(studyId, filename);
                    }
                });
            });
        }

        // Delete a file from the study
        async function deleteStudyFile(studyId, filename) {
            try {
                const response = await fetch(`/api/study/${studyId}/files/${filename}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error('Failed to delete file');
                }

                showTempMessage(`Deleted ${filename}`);

                // Reload study data
                await loadStudyData(studyId);
            } catch (error) {
                console.error('Error deleting file:', error);
                showTempMessage('Error deleting file: ' + error.message);
            }
        }

        // Update patient information
        function updatePatientInfo(files) {
            // Find the first file with patient information
            const fileWithPatientInfo = files.find(file =>
                file.patient_info && Object.keys(file.patient_info).length > 0
            );

            if (!fileWithPatientInfo) return;

            const patientInfo = fileWithPatientInfo.patient_info;

            // Update patient info fields
            if (patientInfo.name) {
                document.getElementById('patient-name').textContent = patientInfo.name;
            }

            if (patientInfo.id) {
                document.getElementById('patient-id').textContent = patientInfo.id;
            }

            if (patientInfo.birthdate) {
                // Format birthdate if needed
                let birthdate = patientInfo.birthdate;
                if (birthdate.length === 8) {
                    // Format YYYYMMDD to YYYY-MM-DD
                    birthdate = `${birthdate.substring(0, 4)}-${birthdate.substring(4, 6)}-${birthdate.substring(6, 8)}`;
                }
                document.getElementById('patient-birthdate').textContent = birthdate;
            }

            if (patientInfo.sex) {
                document.getElementById('patient-sex').textContent = patientInfo.sex;
            }
        }

        // Load signal data
        async function loadSignals(signalFiles) {
            const container = document.getElementById('signals-container');
            container.innerHTML = '';
            signalData = {};
            currentSignals = signalFiles;

            // Only create the signal containers, don't load data yet
            for (const signalFile of signalFiles) {
                // Create canvas for each signal type
                const canvasDiv = document.createElement('div');
                canvasDiv.className = 'signal-canvas';

                // We'll set the height dynamically after loading the actual channel data
                // For now, set a default height
                canvasDiv.style.height = '160px'; // Default height for 1 channel + header + x-axis

                // Format the signal type name for better display
                const signalTypeName = signalFile.type.replace(/([A-Z])/g, ' $1').trim();
                const formattedType = signalTypeName.charAt(0).toUpperCase() + signalTypeName.slice(1);

                canvasDiv.innerHTML = `
                    <div class="signal-header">${formattedType}</div>
                    <div class="signal-plot-area">
                        <div class="channel-labels" id="labels-${signalFile.type}"></div>
                        <canvas id="signal-${signalFile.type}"></canvas>
                        <div class="signal-x-axis" id="axis-${signalFile.type}"></div>
                    </div>
                `;
                container.appendChild(canvasDiv);

                // Initialize empty data structure
                signalData[signalFile.type] = [];
            }
        }

        // Add this new function to load data for the current time window
        async function loadSignalWindow(timeStart, timeEnd) {
            showTempMessage(`Loading data for current window...`);

            const windowPromises = [];

            for (const signalFile of currentSignals) {
                const promise = fetch(`/api/signal/${signalFile.type}?path=${encodeURIComponent(signalFile.path)}&start_time=${timeStart}&window_size=${timeEnd-timeStart}`)
                    .then(response => response.json())
                    .then(channelData => {
                        // Store signal data for this window
                        signalData[signalFile.type] = channelData;

                        // Update the signal container height based on actual channel count
                        // Find the container that has this signal canvas
                        const signalCanvas = document.getElementById(`signal-${signalFile.type}`);
                        if (signalCanvas && channelData.length > 0) {
                            const signalContainer = signalCanvas.closest('.signal-canvas');
                            if (signalContainer) {
                                const channelCount = channelData.length;
                                const signalHeight = (channelCount * 100) + 60; // 100px per channel + 60px for header and x-axis
                                signalContainer.style.height = `${signalHeight}px`;
                            }
                        }
                    });

                windowPromises.push(promise);
            }

            // Wait for all signal data to load
            await Promise.all(windowPromises);
        }

        // Show temporary message
        function showTempMessage(message) {
            // Remove any existing message
            const existingMsg = document.querySelector('.temp-message');
            if (existingMsg) {
                existingMsg.remove();
            }

            // Create new message
            const msgDiv = document.createElement('div');
            msgDiv.className = 'temp-message';
            msgDiv.textContent = message;
            document.body.appendChild(msgDiv);

            // Remove after 2 seconds
            setTimeout(() => {
                msgDiv.style.opacity = '0';
                setTimeout(() => msgDiv.remove(), 500);
            }, 2000);
        }

        function getEventTypeInfo(annotation) {
            // Initialize default return values
            let result = {
                category: 'unknown',
                displayName: annotation.type_name || annotation.type,
                color: 'rgba(128, 128, 128, 0.2)', // Default gray
                borderColor: 'rgba(128, 128, 128, 0.8)',
                visible: true
            };

            // Check for sleep stage events by code
            if (annotation.type === '2:23672' ||
                annotation.type === '130834' ||
                annotation.type === '130835' ||
                annotation.type === '130836' ||
                annotation.type === '2:23680') {
                result.category = 'sleepStage';
                // Make sleep stages more transparent (0.1 instead of 0.2)
                result.color = 'rgba(0, 0, 255, 0.1)'; // Light blue with higher transparency
                result.borderColor = 'rgba(0, 0, 255, 0.4)'; // Also make borders less dominant

                // Map sleep stage codes to readable names
                if (annotation.type === '2:23672') {
                    result.displayName = 'Wake';
                } else if (annotation.type === '130834') {
                    result.displayName = 'N1';
                } else if (annotation.type === '130835') {
                    result.displayName = 'N2';
                } else if (annotation.type === '130836') {
                    result.displayName = 'N3';
                } else if (annotation.type === '2:23680') {
                    result.displayName = 'REM';
                    result.color = 'rgba(255, 0, 0, 0.1)'; // Very light red
                    result.borderColor = 'rgba(255, 0, 0, 0.4)';
                }

                result.visible = true; // Sleep stages are always visible
            }
            // Check for respiratory/apnea events
            else if (annotation.type === '3:3072') {
                result.category = 'respiratory';
                result.color = 'rgba(255, 0, 0, 0.2)'; // Red
                result.borderColor = 'rgba(255, 0, 0, 0.8)';
                result.visible = document.getElementById('show-respiratory').checked;
            }
            // Check for arousal events
            else if (annotation.type === '2:23800') {
                result.category = 'arousal';
                result.color = 'rgba(255, 165, 0, 0.2)'; // Orange
                result.borderColor = 'rgba(255, 165, 0, 0.8)';
                result.visible = document.getElementById('show-arousals').checked;
            }
            // Check for leg/limb movement events
            else if (annotation.type === '2:24184') {
                result.category = 'limb';
                result.color = 'rgba(0, 128, 0, 0.2)'; // Green
                result.borderColor = 'rgba(0, 128, 0, 0.8)';
                result.visible = document.getElementById('show-leg-movements').checked;
            }

            return result;
        }

        // Draw signal data
        function drawSignal(signalType, timeStart, timeEnd) {
            if (!signalData[signalType]) return;

            const canvas = document.getElementById(`signal-${signalType}`);
            const ctx = canvas.getContext('2d');
            const labelsContainer = document.getElementById(`labels-${signalType}`);

            // Set canvas size - use full height of plot area minus the x-axis
            canvas.width = canvas.parentElement.clientWidth - 110; // Account for labels width with extra padding
            canvas.height = canvas.parentElement.clientHeight - 30; // Leave exactly 30px for x-axis
            canvas.style.marginLeft = '110px'; // Offset canvas to make room for labels

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            labelsContainer.innerHTML = ''; // Clear existing labels

            const channels = signalData[signalType];
            if (channels.length === 0) return;

            // For each channel in this signal type
            channels.forEach((channel, idx) => {
                const samplingRate = channel.sampling_rate;

                // Use the data we've already loaded for this time window
                const visibleData = channel.data;

                // Normalize the data
                const maxValue = Math.max(...visibleData.map(d => Math.abs(d))) || 1;

                // Use consistent height for each channel (100px per channel)
                const fixedChannelHeight = 100; // Fixed height per channel
                // Add a small buffer between channels to prevent overlap
                const buffer = 10; // Consistent buffer
                const adjustedChannelHeight = fixedChannelHeight - buffer;
                const yOffset = idx * fixedChannelHeight + adjustedChannelHeight/2 + buffer;

                // Draw the signal
                ctx.beginPath();
                ctx.moveTo(0, yOffset);

                for (let i = 0; i < visibleData.length; i++) {
                    const x = (i / visibleData.length) * canvas.width;
                    // Use consistent amplitude scaling for all channels
                    const amplitude = (visibleData[i] / maxValue) * (adjustedChannelHeight/2 - 15);
                    const y = yOffset - amplitude;
                    ctx.lineTo(x, y);
                }

                // Use different colors for different channels in the same group
                const colors = ['#2196F3', '#4CAF50', '#9C27B0', '#FF5722', '#795548'];
                ctx.strokeStyle = colors[idx % colors.length];
                ctx.lineWidth = 1.5; // Slightly thicker line for better visibility
                ctx.stroke();

                // Add channel label to the labels container instead of on the canvas
                const labelDiv = document.createElement('div');
                labelDiv.className = 'channel-label';
                labelDiv.textContent = channel.name;
                labelDiv.style.position = 'absolute';
                // Consistent positioning based on fixed channel height
                labelDiv.style.top = `${idx * fixedChannelHeight + (fixedChannelHeight/2) - 20}px`;
                // Add a small indicator of the signal color - use the same colors array defined above
                labelDiv.style.borderLeftColor = colors[idx % colors.length];

                // Add units if available
                const unitsSpan = document.createElement('span');
                unitsSpan.className = 'channel-units';
                unitsSpan.textContent = channel.units || 'μV'; // Default to microvolts if not specified
                labelDiv.appendChild(unitsSpan);

                // Add value range
                const rangeSpan = document.createElement('span');
                rangeSpan.className = 'channel-range';
                rangeSpan.textContent = `Range: ±${maxValue.toFixed(1)} ${channel.units || 'μV'}`;
                labelDiv.appendChild(rangeSpan);

                labelsContainer.appendChild(labelDiv);
            });

            // Draw x-axis timestamps - ensure this is visible
            drawTimeAxis(signalType, timeStart, timeEnd);

            // Make sure the axis is visible and positioned correctly
            const axisDiv = document.getElementById(`axis-${signalType}`);
            axisDiv.style.display = 'block';
            axisDiv.style.position = 'absolute';
            axisDiv.style.bottom = '0';
            axisDiv.style.top = 'auto';

            // Draw visible annotations on the signal
            drawAnnotationsOnSignal(signalType, timeStart, timeEnd);

            // Add hover functionality to show signal values
            addSignalValueHover(signalType, timeStart, timeEnd);
        }

        // Draw time axis for a signal
        function drawTimeAxis(signalType, timeStart, timeEnd) {
            const axisDiv = document.getElementById(`axis-${signalType}`);
            axisDiv.innerHTML = '';

            const duration = timeEnd - timeStart;
            // Calculate appropriate number of ticks based on duration
            // For shorter windows, show more detail
            let numTicks;
            if (duration <= 10) {
                numTicks = duration; // One tick per second for short windows
            } else if (duration <= 30) {
                numTicks = Math.floor(duration / 2); // One tick every 2 seconds for medium windows
            } else if (duration <= 60) {
                numTicks = Math.floor(duration / 5); // One tick every 5 seconds for larger windows
            } else {
                numTicks = Math.floor(duration / 10); // One tick every 10 seconds for very large windows
            }

            // Ensure we have at least 5 ticks and no more than 15
            numTicks = Math.max(5, Math.min(numTicks, 15));

            // Add a title to the axis - centered at the bottom
            const axisTitle = document.createElement('div');
            axisTitle.style.position = 'absolute';
            axisTitle.style.left = '50%';
            axisTitle.style.transform = 'translateX(-50%)';
            axisTitle.style.bottom = '0';
            axisTitle.style.fontSize = '10px';
            axisTitle.style.fontWeight = 'bold';
            axisTitle.style.color = '#666';
            axisTitle.textContent = 'Time (mm:ss)';
            axisDiv.appendChild(axisTitle);

            // Draw the main axis line
            const axisLine = document.createElement('div');
            axisLine.style.position = 'absolute';
            axisLine.style.left = '0';
            axisLine.style.right = '0';
            axisLine.style.top = '0'; // Position at the top to be right below the signals
            axisLine.style.height = '2px';
            axisLine.style.backgroundColor = '#666';
            axisDiv.appendChild(axisLine);

            for (let i = 0; i <= numTicks; i++) {
                const position = (i / numTicks);
                const time = timeStart + position * duration;
                const minutes = Math.floor(time / 60);
                const seconds = Math.floor(time % 60);

                // Create tick mark
                const tickMark = document.createElement('div');
                tickMark.className = 'time-tick-mark';
                tickMark.style.left = `${position * 100}%`;
                tickMark.style.top = '0'; // Start from the top
                tickMark.style.height = '8px'; // Make tick marks taller
                axisDiv.appendChild(tickMark);

                // Create tick label - position directly under the signal
                const tickLabel = document.createElement('div');
                tickLabel.className = 'time-tick';
                tickLabel.style.left = `${position * 100}%`;
                tickLabel.style.top = '10px'; // Position right below the tick marks
                tickLabel.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                axisDiv.appendChild(tickLabel);
            }
        }

        // Draw annotations on a signal
        function drawAnnotationsOnSignal(signalType, timeStart, timeEnd) {
            const canvas = document.getElementById(`signal-${signalType}`);
            const ctx = canvas.getContext('2d');

            // Remove any existing tooltips
            document.querySelectorAll('.annotation-tooltip').forEach(el => el.remove());

            // Find annotations in the current time window
            const visibleAnnotations = annotations.filter(a => {
                return (a.start >= timeStart && a.start < timeEnd) ||
                    (a.end > timeStart && a.end <= timeEnd) ||
                    (a.start <= timeStart && a.end >= timeEnd);
            });

            // Draw each annotation
            visibleAnnotations.forEach(annotation => {
                // Get event type information
                const eventInfo = getEventTypeInfo(annotation);

                // Skip if not visible
                if (!eventInfo.visible) return;

                // Calculate position
                const startX = Math.max(0, ((annotation.start - timeStart) / (timeEnd - timeStart)) * canvas.width);
                const endX = Math.min(canvas.width, ((annotation.end - timeStart) / (timeEnd - timeStart)) * canvas.width);

                // Draw annotation background with gradient for better visual appearance
                const gradient = ctx.createLinearGradient(startX, 0, endX, 0);
                gradient.addColorStop(0, eventInfo.color);
                gradient.addColorStop(1, eventInfo.color.replace('0.2', '0.1')); // Fade out slightly
                ctx.fillStyle = gradient;
                ctx.fillRect(startX, 0, endX - startX, canvas.height);

                // Draw annotation border with rounded corners for better appearance
                ctx.strokeStyle = eventInfo.borderColor;
                ctx.lineWidth = 1;
                // Just draw vertical lines at start and end instead of full rectangle
                ctx.beginPath();
                ctx.moveTo(startX, 0);
                ctx.lineTo(startX, canvas.height);
                ctx.moveTo(endX, 0);
                ctx.lineTo(endX, canvas.height);
                ctx.stroke();

                // Draw annotation label if it fits
                if (endX - startX > 40) {
                    // Measure text first
                    ctx.font = 'bold 10px Arial';
                    const textWidth = ctx.measureText(eventInfo.displayName).width;

                    // Draw background with rounded corners
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                    const bgX = startX + 2;

                    // Position sleep stage labels higher than other annotations
                    const bgY = eventInfo.category === 'sleepStage' ? 5 : 25;
                    const bgWidth = textWidth + 6;
                    const bgHeight = 14;
                    const radius = 3;

                    // Draw rounded rectangle
                    ctx.beginPath();
                    ctx.moveTo(bgX + radius, bgY);
                    ctx.lineTo(bgX + bgWidth - radius, bgY);
                    ctx.quadraticCurveTo(bgX + bgWidth, bgY, bgX + bgWidth, bgY + radius);
                    ctx.lineTo(bgX + bgWidth, bgY + bgHeight - radius);
                    ctx.quadraticCurveTo(bgX + bgWidth, bgY + bgHeight, bgX + bgWidth - radius, bgY + bgHeight);
                    ctx.lineTo(bgX + radius, bgY + bgHeight);
                    ctx.quadraticCurveTo(bgX, bgY + bgHeight, bgX, bgY + bgHeight - radius);
                    ctx.lineTo(bgX, bgY + radius);
                    ctx.quadraticCurveTo(bgX, bgY, bgX + radius, bgY);
                    ctx.closePath();
                    ctx.fill();

                    // Draw text with slight shadow for better readability
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
                    ctx.fillText(eventInfo.displayName, startX + 4, bgY + 11);
                    ctx.fillStyle = 'black';
                    ctx.fillText(eventInfo.displayName, startX + 3, bgY + 10);
                }

                // Add hover functionality for annotations
                const annotationArea = document.createElement('div');
                annotationArea.style.position = 'absolute';
                annotationArea.style.left = `${startX + 100}px`; // Account for label container
                annotationArea.style.top = '0';
                annotationArea.style.width = `${endX - startX}px`;
                annotationArea.style.height = `${canvas.height}px`;
                annotationArea.style.zIndex = '5';
                annotationArea.style.cursor = 'pointer';

                // Add tooltip on hover
                annotationArea.addEventListener('mouseover', (e) => {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'annotation-tooltip';
                    tooltip.textContent = `${eventInfo.displayName} (${Math.floor(annotation.start / 60)}:${Math.floor(annotation.start % 60).toString().padStart(2, '0')} - ${Math.floor(annotation.end / 60)}:${Math.floor(annotation.end % 60).toString().padStart(2, '0')})`;
                    tooltip.style.left = `${e.clientX + 10}px`;
                    tooltip.style.top = `${e.clientY + 10}px`;
                    document.body.appendChild(tooltip);
                });

                annotationArea.addEventListener('mousemove', (e) => {
                    const tooltip = document.querySelector('.annotation-tooltip');
                    if (tooltip) {
                        tooltip.style.left = `${e.clientX + 10}px`;
                        tooltip.style.top = `${e.clientY + 10}px`;
                    }
                });

                annotationArea.addEventListener('mouseout', () => {
                    const tooltip = document.querySelector('.annotation-tooltip');
                    if (tooltip) tooltip.remove();
                });

                // Navigate to annotation on click
                annotationArea.addEventListener('click', () => {
                    navigateToTime(annotation.start);
                });

                canvas.parentElement.appendChild(annotationArea);
            });
        }

        // Draw hypnogram
        function drawHypnogram(annotations) {
            const canvas = document.getElementById('hypnogram-canvas');
            canvas.width = canvas.parentElement.clientWidth;
            canvas.height = canvas.parentElement.clientHeight;

            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Find sleep stage annotations
            const sleepStages = annotations.filter(a =>
                a.type === '2:23672' || // Wake
                a.type === '130834' ||  // N1
                a.type === '130835' ||  // N2
                a.type === '130836' ||  // N3
                a.type === '2:23680'    // REM
            );

            if (sleepStages.length === 0) return;

            // Find start and end times
            const startTime = Math.min(...annotations.map(a => a.start));
            const endTime = Math.max(...annotations.map(a => a.start + a.duration));
            const totalDuration = endTime - startTime;

            // Map sleep stages to y-positions
            const stageMap = {
                '2:23680': 5, // REM
                '2:23672': 4, // Wake
                '130834': 3,  // N1
                '130835': 2,  // N2
                '130836': 1,  // N3
            };

            // Draw sleep stages
            let lastX = 0;
            let lastY = canvas.height - (2 * 20); // Default to wake position
            let lastStageType = null;

            sleepStages.forEach(stage => {
                const x = ((stage.start - startTime) / totalDuration) * canvas.width;
                const stageLevel = stageMap[stage.type] || 2; // Default if unknown
                const y = canvas.height - (stageLevel * 20);

                // Draw vertical transition line (always black)
                ctx.beginPath();
                ctx.moveTo(x, lastY);
                ctx.lineTo(x, y);
                ctx.strokeStyle = 'black';
                ctx.lineWidth = 2;
                ctx.stroke();

                // Draw horizontal plateau line with appropriate color
                if (lastX > 0) {
                    ctx.beginPath();
                    ctx.moveTo(lastX, lastY);
                    ctx.lineTo(x, lastY);

                    // Color REM plateaus red, others black
                    if (lastStageType === '2:23680') { // REM
                        ctx.strokeStyle = 'red';
                    } else {
                        ctx.strokeStyle = 'black';
                    }
                    ctx.lineWidth = 2;
                    ctx.stroke();
                }

                lastX = x;
                lastY = y;
                lastStageType = stage.type;
            });

            // Draw the final segment to the end of canvas
            ctx.beginPath();
            ctx.moveTo(lastX, lastY);
            ctx.lineTo(canvas.width, lastY);
            if (lastStageType === '2:23680') { // REM
                ctx.strokeStyle = 'red';
            } else {
                ctx.strokeStyle = 'black';
            }
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw labels
            ctx.font = '12px Arial';
            ctx.fillText('REM', 5, canvas.height - 90);
            ctx.fillText('Wake', 5, canvas.height - 70);
            ctx.fillText('N1', 5, canvas.height - 50);
            ctx.fillText('N2', 5, canvas.height - 30);
            ctx.fillText('N3', 5, canvas.height - 10);

            // Draw additional events by category
            const respiratoryEvents = annotations.filter(a => getEventTypeInfo(a).category === 'respiratory' && getEventTypeInfo(a).visible);
            drawEventsOnHypnogram(ctx, respiratoryEvents, startTime, totalDuration, canvas.width, canvas.height, 'red');

            const limbEvents = annotations.filter(a => getEventTypeInfo(a).category === 'limb' && getEventTypeInfo(a).visible);
            drawEventsOnHypnogram(ctx, limbEvents, startTime, totalDuration, canvas.width, canvas.height, 'green');

            const arousalEvents = annotations.filter(a => getEventTypeInfo(a).category === 'arousal' && getEventTypeInfo(a).visible);
            drawEventsOnHypnogram(ctx, arousalEvents, startTime, totalDuration, canvas.width, canvas.height, 'orange');

            // Add click handler for navigation
            canvas.addEventListener('click', (e) => {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const clickedTime = startTime + (x / canvas.width) * totalDuration;
                navigateToTime(clickedTime);
            });
        }

        // Draw events on the hypnogram
        function drawEventsOnHypnogram(ctx, events, startTime, totalDuration, canvasWidth, canvasHeight, color) {
            events.forEach(event => {
                const x = ((event.start - startTime) / totalDuration) * canvasWidth;
                const width = (event.duration / totalDuration) * canvasWidth;

                // Draw event marker
                ctx.fillStyle = color;
                ctx.fillRect(x, 0, width, 10);

                // Draw vertical line
                ctx.strokeStyle = color;
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvasHeight);
                ctx.stroke();
            });
        }

        // Populate annotation list
        function populateAnnotations(annotations) {
            const list = document.getElementById('annotation-list');
            list.innerHTML = '';

            annotations.sort((a, b) => a.start - b.start);

            annotations.forEach(annotation => {
                const item = document.createElement('div');
                item.className = 'annotation-item';
                const minutes = Math.floor(annotation.start / 60);
                const seconds = Math.floor(annotation.start % 60);

                // Get event type information from centralized function
                const eventInfo = getEventTypeInfo(annotation);

                // Use type_name for display if available, otherwise use displayName from eventInfo
                item.textContent = `[${minutes}:${seconds.toString().padStart(2, '0')}] ${eventInfo.displayName}`;

                // Apply border color based on event type
                item.style.borderLeft = `4px solid ${eventInfo.borderColor}`;

                // Add data attributes for filtering
                item.dataset.category = eventInfo.category;
                item.dataset.eventType = annotation.type;

                // Add click handler for navigation
                item.addEventListener('click', () => {
                    navigateToTime(annotation.start);
                });

                list.appendChild(item);
            });

            // After populating, update the visibility of items based on current filter settings
            updateAnnotationListVisibility();
        }

        // Add this helper function to update the visibility of annotations in the list
        function updateAnnotationListVisibility() {
            const showRespiratory = document.getElementById('show-respiratory').checked;
            const showLegMovements = document.getElementById('show-leg-movements').checked;
            const showArousals = document.getElementById('show-arousals').checked;

            const items = document.querySelectorAll('.annotation-item');

            items.forEach(item => {
                const category = item.dataset.category;

                if (category === 'respiratory' && !showRespiratory) {
                    item.style.opacity = '0.5';
                } else if (category === 'limb' && !showLegMovements) {
                    item.style.opacity = '0.5';
                } else if (category === 'arousal' && !showArousals) {
                    item.style.opacity = '0.5';
                } else {
                    item.style.opacity = '1';
                }
            });
        }

        // Navigate to specific time
        function navigateToTime(timeInSeconds) {
            currentStartTime = timeInSeconds - (windowSizeInSeconds / 2);
            if (currentStartTime < 0) currentStartTime = 0;
            updateView();
            showTempMessage(`Navigated to ${Math.floor(timeInSeconds / 60)}:${Math.floor(timeInSeconds % 60).toString().padStart(2, '0')}`);
        }

        // Updated view function that actually renders the current time window
        async function updateView() {
            // Calculate end time of current window
            const timeEnd = currentStartTime + windowSizeInSeconds;

            // Load data for this window
            await loadSignalWindow(currentStartTime, timeEnd);

            // For each signal type, draw the current window
            for (const signalType in signalData) {
                drawSignal(signalType, currentStartTime, timeEnd);
            }

            // Highlight the current window in the hypnogram
            highlightHypnogramWindow();

            // Highlight visible annotations
            highlightVisibleAnnotations();

            // Show current time window info
            const startMinutes = Math.floor(currentStartTime / 60);
            const startSeconds = Math.floor(currentStartTime % 60);
            const endMinutes = Math.floor(timeEnd / 60);
            const endSeconds = Math.floor(timeEnd % 60);

            showTempMessage(`Viewing window: ${startMinutes}:${startSeconds.toString().padStart(2, '0')} - ${endMinutes}:${endSeconds.toString().padStart(2, '0')}`);
        }

        // Highlight current window in hypnogram
        function highlightHypnogramWindow() {
            const canvas = document.getElementById('hypnogram-canvas');
            const ctx = canvas.getContext('2d');

            // First redraw the hypnogram
            drawHypnogram(annotations);

            // Find start and end times for scaling
            const startTime = Math.min(...annotations.map(a => a.start));
            const endTime = Math.max(...annotations.map(a => a.start + a.duration));
            const totalDuration = endTime - startTime;

            // Calculate window position
            const windowStartX = ((currentStartTime - startTime) / totalDuration) * canvas.width;
            const windowEndX = ((currentStartTime + windowSizeInSeconds - startTime) / totalDuration) * canvas.width;

            // Draw window highlight
            ctx.fillStyle = 'rgba(173, 216, 230, 0.3)';  // Light blue with 30% opacity
            ctx.fillRect(windowStartX, 0, windowEndX - windowStartX, canvas.height);

            // Draw window borders
            ctx.strokeStyle = 'rgba(70, 130, 180, 0.8)';  // Steel blue with 80% opacity
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(windowStartX, 0);
            ctx.lineTo(windowStartX, canvas.height);
            ctx.moveTo(windowEndX, 0);
            ctx.lineTo(windowEndX, canvas.height);
            ctx.stroke();
        }

        // Function to add hover functionality to show signal values
        function addSignalValueHover(signalType, timeStart, timeEnd) {
            const canvas = document.getElementById(`signal-${signalType}`);
            const plotArea = canvas.parentElement;
            const channels = signalData[signalType];

            if (!channels || channels.length === 0) return;

            // Remove any existing hover overlay
            const existingOverlay = plotArea.querySelector('.signal-hover-overlay');
            if (existingOverlay) {
                existingOverlay.remove();
            }

            // Create a transparent overlay for mouse tracking
            const overlay = document.createElement('div');
            overlay.className = 'signal-hover-overlay';
            overlay.style.position = 'absolute';
            overlay.style.top = '0';
            overlay.style.left = '110px'; // Match the canvas offset
            overlay.style.width = `${canvas.width}px`;
            overlay.style.height = `${canvas.height}px`;
            overlay.style.zIndex = '20';
            overlay.style.cursor = 'crosshair';

            // Add mouse event listeners
            overlay.addEventListener('mousemove', (e) => {
                // Calculate relative position within the canvas
                const rect = overlay.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // Calculate time at this x position
                const timeRatio = x / canvas.width;
                const currentTime = timeStart + (timeEnd - timeStart) * timeRatio;

                // Format time as mm:ss
                const minutes = Math.floor(currentTime / 60);
                const seconds = Math.floor(currentTime % 60);
                const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                // Find the closest data point for each channel
                let tooltipHTML = '';

                channels.forEach((channel, idx) => {
                    const dataIndex = Math.floor((channel.data.length - 1) * timeRatio);
                    if (dataIndex >= 0 && dataIndex < channel.data.length) {
                        const value = channel.data[dataIndex];
                        const units = channel.units || 'μV';

                        // Add to tooltip content - more compact format
                        tooltipHTML += `<div style="color:${['#2196F3', '#4CAF50', '#9C27B0', '#FF5722', '#795548'][idx % 5]}; margin:2px 0; white-space:nowrap;">
                            <b>${channel.name}:</b> ${value.toFixed(2)} ${units}
                        </div>`;
                    }
                });

                // Add time to tooltip - more compact format
                tooltipHTML += `<div style="margin-top:4px; border-top:1px solid #555; padding-top:3px; text-align:center; font-weight:bold;">
                    ${timeString}
                </div>`;

                // Create a compact tooltip that stays near the signal
                let tooltip = document.querySelector('.signal-tooltip');
                if (!tooltip) {
                    tooltip = document.createElement('div');
                    tooltip.className = 'signal-tooltip';
                    document.body.appendChild(tooltip);
                }

                tooltip.innerHTML = tooltipHTML;

                // Position the tooltip near the cursor but fixed to the signal area
                const plotRect = plotArea.getBoundingClientRect();

                // Calculate position to keep tooltip within the viewport
                let tooltipX = e.clientX + 15;
                let tooltipY = e.clientY - 40; // Position above cursor

                // Keep tooltip within the signal area horizontally
                if (tooltipX + 200 > plotRect.right) { // Assuming max tooltip width is 200px
                    tooltipX = e.clientX - 215; // Position to the left of cursor
                }

                // Keep tooltip within the viewport vertically
                if (tooltipY < 10) {
                    tooltipY = e.clientY + 25; // Position below cursor if too close to top
                }

                tooltip.style.left = `${tooltipX}px`;
                tooltip.style.top = `${tooltipY}px`;

                // Draw vertical line at cursor position
                drawCursorLine(signalType, x);
            });

            overlay.addEventListener('mouseout', () => {
                // Remove tooltip and cursor line when mouse leaves
                const tooltip = document.querySelector('.signal-tooltip');
                if (tooltip) tooltip.remove();

                // Remove cursor line
                removeCursorLine(signalType);
            });

            plotArea.appendChild(overlay);
        }

        // Function to draw a vertical line at cursor position
        function drawCursorLine(signalType, x) {
            const canvas = document.getElementById(`signal-${signalType}`);
            const plotArea = canvas.parentElement;

            // Remove any existing cursor line
            removeCursorLine(signalType);

            // Create cursor line
            const cursorLine = document.createElement('div');
            cursorLine.className = 'signal-cursor-line';
            cursorLine.style.position = 'absolute';
            cursorLine.style.top = '0';
            cursorLine.style.left = `${x + 110}px`; // Add offset to match canvas
            cursorLine.style.width = '2px'; // Slightly thicker for better visibility
            cursorLine.style.height = '100%';
            cursorLine.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
            cursorLine.style.zIndex = '15';
            cursorLine.style.pointerEvents = 'none'; // Ensure it doesn't interfere with mouse events

            // Add a small circle at the intersection with the signal
            const dataPoint = document.createElement('div');
            dataPoint.className = 'signal-data-point';
            dataPoint.style.position = 'absolute';
            dataPoint.style.width = '8px';
            dataPoint.style.height = '8px';
            dataPoint.style.borderRadius = '50%';
            dataPoint.style.backgroundColor = '#2196F3';
            dataPoint.style.border = '1px solid white';
            dataPoint.style.left = `${x + 110 - 4}px`; // Center on the line
            dataPoint.style.top = '50%'; // Will be adjusted by channel data
            dataPoint.style.zIndex = '16';
            dataPoint.style.pointerEvents = 'none';

            plotArea.appendChild(cursorLine);

            // Only add data point if we have channel data
            const channels = signalData[signalType];
            if (channels && channels.length > 0) {
                // For simplicity, just show for the first channel
                plotArea.appendChild(dataPoint);
            }
        }

        // Function to remove cursor line and data point
        function removeCursorLine(signalType) {
            const canvas = document.getElementById(`signal-${signalType}`);
            const plotArea = canvas.parentElement;

            const cursorLine = plotArea.querySelector('.signal-cursor-line');
            if (cursorLine) cursorLine.remove();

            const dataPoint = plotArea.querySelector('.signal-data-point');
            if (dataPoint) dataPoint.remove();
        }

        // Highlight annotations that are visible in the current window
        function highlightVisibleAnnotations() {
            const timeEnd = currentStartTime + windowSizeInSeconds;
            const visibleAnnotations = annotations.filter(a => {
                return (a.start >= currentStartTime && a.start < timeEnd) ||
                       (a.end > currentStartTime && a.end <= timeEnd) ||
                       (a.start <= currentStartTime && a.end >= timeEnd);
            });

            // Update annotation list highlighting
            const items = document.querySelectorAll('.annotation-item');
            items.forEach(item => {
                item.style.backgroundColor = '';
                item.style.fontWeight = 'normal';
            });

            visibleAnnotations.forEach(annotation => {
                const index = annotations.indexOf(annotation);
                if (index >= 0 && index < items.length) {
                    items[index].style.backgroundColor = '#e6f7ff';
                    items[index].style.fontWeight = 'bold';
                }
            });
        }

        // Window navigation
        document.getElementById('previous-window').addEventListener('click', () => {
            currentStartTime -= windowSizeInSeconds;
            if (currentStartTime < 0) currentStartTime = 0;
            updateView();
        });

        document.getElementById('next-window').addEventListener('click', () => {
            currentStartTime += windowSizeInSeconds;
            updateView();
        });

        // Window size change
        document.getElementById('window-size').addEventListener('change', (e) => {
            windowSizeInSeconds = parseInt(e.target.value, 10);
            if (isNaN(windowSizeInSeconds) || windowSizeInSeconds < 5) {
                windowSizeInSeconds = 5;
                e.target.value = 5;
            } else if (windowSizeInSeconds > 120) {
                windowSizeInSeconds = 120;
                e.target.value = 120;
            }
            updateView();
        });

        // Event checkbox handlers
        document.getElementById('show-respiratory').addEventListener('change', () => {
            updateAnnotationListVisibility();
            drawHypnogram(annotations);
            updateView();
        });

        document.getElementById('show-leg-movements').addEventListener('change', () => {
            updateAnnotationListVisibility();
            drawHypnogram(annotations);
            updateView();
        });

        document.getElementById('show-arousals').addEventListener('change', () => {
            updateAnnotationListVisibility();
            drawHypnogram(annotations);
            updateView();
        });

        // Only clean up study when window is actually closed, not on refresh
        // We'll use the pagehide event with the persisted property to determine if it's a refresh or close
        window.addEventListener('pagehide', async (event) => {
            // If persisted is true, it's a page cache and not a true unload (like refresh)
            // We only want to clean up if it's a true unload (window close)
            if (!event.persisted && currentStudyId) {
                // Check if we should clean up or keep for refresh persistence
                const keepForRefresh = localStorage.getItem('currentStudyId') === currentStudyId;

                if (!keepForRefresh) {
                    try {
                        await fetch(`/api/cleanup/${currentStudyId}`, {
                            method: 'DELETE'
                        });
                    } catch (error) {
                        console.error('Error cleaning up study:', error);
                    }
                }
            }
        });

        // Add keyboard shortcuts for navigation
        document.addEventListener('keydown', (e) => {
            // Left arrow key - previous window
            if (e.key === 'ArrowLeft') {
                currentStartTime -= windowSizeInSeconds;
                if (currentStartTime < 0) currentStartTime = 0;
                updateView();
            }
            // Right arrow key - next window
            else if (e.key === 'ArrowRight') {
                currentStartTime += windowSizeInSeconds;
                updateView();
            }
            // F5 key - refresh current view without requiring new file upload
            else if (e.key === 'F5') {
                e.preventDefault(); // Prevent browser refresh
                showTempMessage('Refreshing view...');
                updateView();
            }
        });

        // Add event listeners for sidebar section toggles
        document.getElementById('toggle-patient-info').addEventListener('click', function() {
            const patientInfo = document.getElementById('patient-info');
            if (patientInfo.style.display === 'none') {
                patientInfo.style.display = 'block';
                this.textContent = '[-]';
            } else {
                patientInfo.style.display = 'none';
                this.textContent = '[+]';
            }
        });

        document.getElementById('toggle-files').addEventListener('click', function() {
            const studyFiles = document.getElementById('study-files');
            if (studyFiles.style.display === 'none') {
                studyFiles.style.display = 'block';
                this.textContent = '[-]';
            } else {
                studyFiles.style.display = 'none';
                this.textContent = '[+]';
            }
        });

        document.getElementById('toggle-annotations').addEventListener('click', function() {
            const annotationList = document.getElementById('annotation-list');
            if (annotationList.style.display === 'none') {
                annotationList.style.display = 'block';
                this.textContent = '[-]';
            } else {
                annotationList.style.display = 'none';
                this.textContent = '[+]';
            }
        });

        // Add event listener for the "Add Files" button
        document.getElementById('add-files-btn').addEventListener('click', function() {
            document.getElementById('add-files-input').click();
        });

        // Handle file selection for adding files to study
        document.getElementById('add-files-input').addEventListener('change', async function() {
            if (this.files.length === 0) return;

            const formData = new FormData();
            Array.from(this.files).forEach(file => {
                formData.append('files[]', file);
            });

            try {
                showTempMessage('Adding files to study...');

                const response = await fetch(`/api/study/${currentStudyId}/files`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showTempMessage(`Added ${result.added_files} files to study`);
                    // Reload study data
                    await loadStudyData(currentStudyId);
                } else {
                    showTempMessage('Error: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error adding files:', error);
                showTempMessage('Error adding files: ' + error.message);
            }

            // Clear the file input
            this.value = '';
        });
    </script>
</body>
</html>